# 历史地球API

这是一个基于Spring Boot、MyBatis和MySQL构建的历史地球应用微服务API。

## 环境要求

- JDK 17+
- Maven 3.6+
- MySQL 5.7+ 或 MySQL 8.0+

## 数据库设置

1. 创建名为`historical_globe`的MySQL数据库
2. 运行`src/main/resources/schema.sql`中的SQL脚本初始化数据库架构

## 配置

数据库连接设置可以在`src/main/resources/application.yml`中配置：

```yaml
spring:
  datasource:
    url: ****************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
```

## 构建和运行应用

1. 构建应用：
   ```
   mvn clean package
   ```

2. 运行应用：
   ```
   java -jar target/historical-globe-api-0.0.1-SNAPSHOT.jar
   ```

   或使用Maven：
   ```
   mvn spring-boot:run
   ```

## API文档

API文档通过Swagger UI提供：

- Swagger UI: http://localhost:8080/swagger-ui.html
- OpenAPI JSON: http://localhost:8080/api-docs

## API端点

所有API操作都通过POST请求发送到相应的端点，并在请求体中包含操作类型和数据。

| 端点                  | 描述           |
|----------------------|-----------------------|
| /api/users/query     | 用户查询操作（获取所有用户、按ID获取用户） |
| /api/users/command   | 用户命令操作（创建、更新、删除用户） |

## 数据格式说明

- `created_at` 和 `updated_at` 字段使用毫秒级时间戳（13位长整型）
- 创建用户时，系统会自动设置 `created_at` 和 `updated_at` 为当前时间戳
- 更新用户时，系统会自动更新 `updated_at` 为当前时间戳

## API使用示例

### 获取所有用户
```
POST http://localhost:8080/api/users/query
Content-Type: application/json

{
  "operation": "findAll"
}
```

### 按ID获取用户
```
POST http://localhost:8080/api/users/query
Content-Type: application/json

{
  "operation": "findById",
  "id": 1
}
```

### 创建新用户
```
POST http://localhost:8080/api/users/command
Content-Type: application/json

{
  "operation": "create",
  "data": {
    "username": "newuser",
    "email": "<EMAIL>",
    "password": "password123"
  }
}
```

### 更新用户
```
POST http://localhost:8080/api/users/command
Content-Type: application/json

{
  "operation": "update",
  "id": 1,
  "data": {
    "username": "updateduser",
    "email": "<EMAIL>",
    "password": "newpassword"
  }
}
```

### 删除用户
```
POST http://localhost:8080/api/users/command
Content-Type: application/json

{
  "operation": "delete",
  "id": 1
}
```

## 响应示例

### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": 1,
    "username": "user1",
    "email": "<EMAIL>",
    "password": "password1",
    "createdAt": 1634720400000,
    "updatedAt": 1634720400000
  }
}
```

### 错误响应
```json
{
  "code": 404,
  "message": "未找到User：id为'1'的记录不存在",
  "data": null
}
```