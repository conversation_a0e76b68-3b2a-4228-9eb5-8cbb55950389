package com.celt.historcal.globe.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建事件命令")
public class CreateEventCommand {
    
    @Schema(description = "年份", example = "2023", required = true)
    private Integer year;
    
    @Schema(description = "事件标题ID", example = "EVENT_001")
    private String titleId;
    
    @Schema(description = "事件标题", example = "重要历史事件", required = true)
    private String title;
    
    @Schema(description = "事件描述", example = "这是一个重要的历史事件描述")
    private String description;
    
    @Schema(description = "事件发生地点", example = "北京")
    private String location;
    
    @Schema(description = "国家ID", example = "CN")
    private String countryId;
    
    @Schema(description = "事件重要性（1-10，10为最重要）", example = "8")
    private Integer importance;
}
