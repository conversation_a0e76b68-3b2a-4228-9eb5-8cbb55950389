package com.celt.historcal.globe.api.mapper;

import com.celt.historcal.globe.api.entity.Event;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface EventMapper {
    
    @Select("SELECT * FROM events ORDER BY year DESC, importance DESC")
    List<Event> findAll();
    
    @Select("SELECT * FROM events WHERE id = #{id}")
    Event findById(Long id);
    
    @Select("SELECT * FROM events WHERE year = #{year} ORDER BY importance DESC")
    List<Event> findByYear(Integer year);
    
    @Select("SELECT * FROM events WHERE country_id = #{countryId} ORDER BY year DESC, importance DESC")
    List<Event> findByCountryId(String countryId);
    
    @Select("SELECT * FROM events WHERE importance >= #{importance} ORDER BY year DESC, importance DESC")
    List<Event> findByImportanceGreaterThanEqual(Integer importance);
    
    @Select("SELECT * FROM events WHERE year BETWEEN #{startYear} AND #{endYear} ORDER BY year DESC, importance DESC")
    List<Event> findByYearRange(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);
    
    @Insert("INSERT INTO events(year, title_id, title, description, location, country_id, importance, created_at, updated_at) " +
            "VALUES(#{year}, #{titleId}, #{title}, #{description}, #{location}, #{countryId}, #{importance}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Event event);

    @Update("UPDATE events SET year = #{year}, title_id = #{titleId}, title = #{title}, " +
            "description = #{description}, location = #{location}, country_id = #{countryId}, " +
            "importance = #{importance}, updated_at = #{updatedAt} WHERE id = #{id}")
    int update(Event event);
    
    @Delete("DELETE FROM events WHERE id = #{id}")
    int deleteById(Long id);
}
