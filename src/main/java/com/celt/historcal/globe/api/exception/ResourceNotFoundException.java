package com.celt.historcal.globe.api.exception;

public class ResourceNotFoundException extends RuntimeException {
    
    public ResourceNotFoundException(String message) {
        super(message);
    }
    
    public ResourceNotFoundException(String resourceName, String fieldName, Object fieldValue) {
        super(String.format("未找到%s：%s为'%s'的记录不存在", resourceName, fieldName, fieldValue));
    }
}