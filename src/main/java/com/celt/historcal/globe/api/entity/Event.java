package com.celt.historcal.globe.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "事件实体")
public class Event {
    @Schema(description = "事件ID", example = "1")
    private Long id;
    
    @Schema(description = "年份", example = "2023", required = true)
    private Integer year;
    
    @Schema(description = "事件标题ID", example = "EVENT_001")
    private String titleId;
    
    @Schema(description = "事件标题", example = "重要历史事件", required = true)
    private String title;
    
    @Schema(description = "事件描述", example = "这是一个重要的历史事件描述")
    private String description;
    
    @Schema(description = "事件发生地点", example = "北京")
    private String location;
    
    @Schema(description = "国家ID", example = "CN")
    private String countryId;
    
    @Schema(description = "事件重要性（1-10，10为最重要）", example = "8")
    private Integer importance;
    
    @Schema(description = "创建时间（毫秒时间戳）", example = "1634720400000")
    private Long createTime;
    
    @Schema(description = "更新时间（毫秒时间戳）", example = "1634720400000")
    private Long updateTime;
}
