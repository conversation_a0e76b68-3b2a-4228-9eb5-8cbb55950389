package com.celt.historcal.globe.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户实体")
public class User {
    @Schema(description = "用户ID", example = "1")
    private Long id;
    
    @Schema(description = "用户名", example = "zhangsan", requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;
    
    @Schema(description = "电子邮箱", example = "<EMAIL>", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;
    
    @Schema(description = "密码（加密后）", example = "password123", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;
    
    @Schema(description = "创建时间（毫秒时间戳）", example = "1634720400000")
    private Long createdAt;
    
    @Schema(description = "更新时间（毫秒时间戳）", example = "1634720400000")
    private Long updatedAt;
}