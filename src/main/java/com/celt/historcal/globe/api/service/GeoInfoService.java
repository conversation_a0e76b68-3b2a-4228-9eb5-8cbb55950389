package com.celt.historcal.globe.api.service;

import com.celt.historcal.globe.api.entity.GeoInfo;
import com.celt.historcal.globe.api.mapper.GeoInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class GeoInfoService {

    private final GeoInfoMapper geoInfoMapper;

    @Autowired
    public GeoInfoService(GeoInfoMapper geoInfoMapper) {
        this.geoInfoMapper = geoInfoMapper;
    }

    public List<GeoInfo> findAllGeoInfos() {
        return geoInfoMapper.findAll();
    }

    public GeoInfo findGeoInfoById(Long id) {
        return geoInfoMapper.findById(id);
    }

    public List<GeoInfo> findGeoInfosByYear(Integer year) {
        return geoInfoMapper.findByYear(year);
    }

    public List<GeoInfo> findGeoInfosByNameContaining(String name) {
        return geoInfoMapper.findByNameContaining(name);
    }

    public List<GeoInfo> findGeoInfosByAbbr(String abbr) {
        return geoInfoMapper.findByAbbr(abbr);
    }

    public List<GeoInfo> findGeoInfosBySubjectTo(String subjectTo) {
        return geoInfoMapper.findBySubjectTo(subjectTo);
    }

    public List<GeoInfo> findGeoInfosByPartOf(String partOf) {
        return geoInfoMapper.findByPartOf(partOf);
    }

    public List<GeoInfo> findGeoInfosByBorderPrecision(Integer borderPrecision) {
        return geoInfoMapper.findByBorderPrecisionGreaterThanEqual(borderPrecision);
    }

    public List<GeoInfo> findGeoInfosByYearRange(Integer startYear, Integer endYear) {
        return geoInfoMapper.findByYearRange(startYear, endYear);
    }

    @Transactional
    public GeoInfo createGeoInfo(GeoInfo geoInfo) {
        // 设置创建时间和更新时间为当前时间戳（毫秒）
        long currentTimeMillis = System.currentTimeMillis();
        geoInfo.setCreatedAt(currentTimeMillis);
        geoInfo.setUpdatedAt(currentTimeMillis);
        geoInfoMapper.insert(geoInfo);
        return geoInfo;
    }

    @Transactional
    public GeoInfo updateGeoInfo(GeoInfo geoInfo) {
        // 设置更新时间为当前时间戳（毫秒）
        geoInfo.setUpdatedAt(System.currentTimeMillis());
        geoInfoMapper.update(geoInfo);
        return geoInfo;
    }

    @Transactional
    public void deleteGeoInfo(Long id) {
        geoInfoMapper.deleteById(id);
    }

    @Transactional
    public List<GeoInfo> batchCreateGeoInfos(List<GeoInfo> geoInfos) {
        // 设置创建时间和更新时间为当前时间戳（毫秒）
        long currentTimeMillis = System.currentTimeMillis();

        for (GeoInfo geoInfo : geoInfos) {
            geoInfo.setCreatedAt(currentTimeMillis);
            geoInfo.setUpdatedAt(currentTimeMillis);
            geoInfoMapper.insert(geoInfo);
        }

        return geoInfos;
    }
}
