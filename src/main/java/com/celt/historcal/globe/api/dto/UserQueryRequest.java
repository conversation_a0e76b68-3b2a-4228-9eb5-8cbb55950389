package com.celt.historcal.globe.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户查询请求")
public class UserQueryRequest {
    
    @Schema(description = "操作类型：findAll-获取所有用户, findById-按ID获取用户", example = "findAll", required = true)
    private String operation;
    
    @Schema(description = "用户ID（当operation为findById时必填）", example = "1")
    private Long id;
}