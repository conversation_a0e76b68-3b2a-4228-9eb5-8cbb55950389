package com.celt.historcal.globe.api.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "地理信息实体")
public class GeoInfo {
    @Schema(description = "地理信息ID", example = "1")
    private Long id;
    
    @Schema(description = "年份", example = "2023", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer year;
    
    @Schema(description = "名称", example = "中华人民共和国")
    private String name;
    
    @Schema(description = "简称", example = "中国")
    private String abbr;
    
    @Schema(description = "隶属于", example = "亚洲")
    private String subjectTo;
    
    @Schema(description = "边界精度", example = "5", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer borderPrecision;
    
    @Schema(description = "属于", example = "联合国")
    private String partOf;
    
    @Schema(description = "坐标信息", example = "经纬度坐标数据")
    private String coordinates;
    
    @Schema(description = "创建时间（毫秒时间戳）", example = "1634720400000")
    private Long createdAt;
    
    @Schema(description = "更新时间（毫秒时间戳）", example = "1634720400000")
    private Long updatedAt;
}
