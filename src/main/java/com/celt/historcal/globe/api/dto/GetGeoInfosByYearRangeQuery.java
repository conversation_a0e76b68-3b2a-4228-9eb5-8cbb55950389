package com.celt.historcal.globe.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "按年份范围获取地理信息查询")
public class GetGeoInfosByYearRangeQuery {
    
    @Schema(description = "开始年份", example = "2020", required = true)
    private Integer startYear;
    
    @Schema(description = "结束年份", example = "2023", required = true)
    private Integer endYear;
}
