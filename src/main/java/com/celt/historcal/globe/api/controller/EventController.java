package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.*;
import com.celt.historcal.globe.api.entity.Event;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.EventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/events")
@Tag(name = "事件管理", description = "历史事件管理相关API")
public class EventController {

    private final EventService eventService;

    @Autowired
    public EventController(EventService eventService) {
        this.eventService = eventService;
    }

    @Operation(summary = "获取所有事件", description = "获取系统中所有历史事件的列表，按年份和重要性排序")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class)))
    })
    @PostMapping("/list")
    public ResponseEntity<ApiResponse<List<Event>>> listEvents() {
        List<Event> events = eventService.findAllEvents();
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按ID获取事件", description = "根据事件ID获取特定历史事件信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/get")
    public ResponseEntity<ApiResponse<Event>> getEventById(
            @Parameter(description = "按ID获取事件查询") @RequestBody GetEventByIdQuery query) {

        if (query.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        Event event = eventService.findEventById(query.getId());
        if (event == null) {
            throw new ResourceNotFoundException("Event", "id", query.getId());
        }

        return ResponseEntity.ok(ApiResponse.success(event));
    }

    @Operation(summary = "按年份获取事件", description = "根据年份获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByYear(
            @Parameter(description = "按年份获取事件查询") @RequestBody GetEventsByYearQuery query) {

        if (query.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        List<Event> events = eventService.findEventsByYear(query.getYear());
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按国家ID获取事件", description = "根据国家ID获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-country")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByCountryId(
            @Parameter(description = "按国家ID获取事件查询") @RequestBody GetEventsByCountryQuery query) {

        if (query.getCountryId() == null || query.getCountryId().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "国家ID不能为空"));
        }

        List<Event> events = eventService.findEventsByCountryId(query.getCountryId());
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按重要性获取事件", description = "根据重要性等级获取历史事件列表（大于等于指定重要性）")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-importance")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByImportance(
            @Parameter(description = "按重要性获取事件查询") @RequestBody GetEventsByImportanceQuery query) {

        if (query.getImportance() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性不能为空"));
        }

        if (query.getImportance() < 1 || query.getImportance() > 10) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
        }

        List<Event> events = eventService.findEventsByImportance(query.getImportance());
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按年份范围获取事件", description = "根据年份范围获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year-range")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByYearRange(
            @Parameter(description = "按年份范围获取事件查询") @RequestBody GetEventsByYearRangeQuery query) {

        if (query.getStartYear() == null || query.getEndYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份和结束年份不能为空"));
        }

        if (query.getStartYear() > query.getEndYear()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份不能大于结束年份"));
        }

        List<Event> events = eventService.findEventsByYearRange(query.getStartYear(), query.getEndYear());
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "创建事件", description = "创建新的历史事件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件创建成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Event>> createEvent(
            @Parameter(description = "创建事件命令") @RequestBody CreateEventCommand command) {

        if (command == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件数据不能为空"));
        }

        if (command.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (command.getTitle() == null || command.getTitle().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件标题不能为空"));
        }

        if (command.getImportance() != null && (command.getImportance() < 1 || command.getImportance() > 10)) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
        }

        // 将command转换为Event实体
        Event event = new Event();
        event.setYear(command.getYear());
        event.setTitleId(command.getTitleId());
        event.setTitle(command.getTitle());
        event.setDescription(command.getDescription());
        event.setLocation(command.getLocation());
        event.setCountryId(command.getCountryId());
        event.setImportance(command.getImportance());

        Event createdEvent = eventService.createEvent(event);
        return ResponseEntity.ok(ApiResponse.success("事件创建成功", createdEvent));
    }

    @Operation(summary = "更新事件", description = "更新现有历史事件信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件更新成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/update")
    public ResponseEntity<ApiResponse<Event>> updateEvent(
            @Parameter(description = "更新事件命令") @RequestBody UpdateEventCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        if (command.getData() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件数据不能为空"));
        }

        Event existingEvent = eventService.findEventById(command.getId());
        if (existingEvent == null) {
            throw new ResourceNotFoundException("Event", "id", command.getId());
        }

        UpdateEventCommand.EventData eventData = command.getData();

        if (eventData.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (eventData.getTitle() == null || eventData.getTitle().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件标题不能为空"));
        }

        if (eventData.getImportance() != null && (eventData.getImportance() < 1 || eventData.getImportance() > 10)) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
        }

        // 构建事件对象
        Event event = new Event();
        event.setId(command.getId());
        event.setYear(eventData.getYear());
        event.setTitleId(eventData.getTitleId());
        event.setTitle(eventData.getTitle());
        event.setDescription(eventData.getDescription());
        event.setLocation(eventData.getLocation());
        event.setCountryId(eventData.getCountryId());
        event.setImportance(eventData.getImportance());

        Event updatedEvent = eventService.updateEvent(event);
        return ResponseEntity.ok(ApiResponse.success("事件更新成功", updatedEvent));
    }

    @Operation(summary = "删除事件", description = "删除指定历史事件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件删除成功",
                    content = @Content(mediaType = "application/json")),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/delete")
    public ResponseEntity<ApiResponse<Object>> deleteEvent(
            @Parameter(description = "删除事件命令") @RequestBody DeleteEventCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        Event eventToDelete = eventService.findEventById(command.getId());
        if (eventToDelete == null) {
            throw new ResourceNotFoundException("Event", "id", command.getId());
        }

        eventService.deleteEvent(command.getId());
        return ResponseEntity.ok(ApiResponse.success("事件删除成功", null));
    }

    @Operation(summary = "批量导入事件", description = "批量导入多个历史事件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "批量导入成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/batch-import")
    public ResponseEntity<ApiResponse<List<Event>>> batchImportEvents(
            @Parameter(description = "批量导入事件命令") @RequestBody BatchImportEventsCommand command) {

        if (command == null || command.getEvents() == null || command.getEvents().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件列表不能为空"));
        }

        // 验证每个事件数据
        for (int i = 0; i < command.getEvents().size(); i++) {
            BatchImportEventsCommand.EventImportData eventData = command.getEvents().get(i);
            String prefix = "第" + (i + 1) + "个事件：";

            if (eventData.getYear() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "年份不能为空"));
            }

            if (eventData.getTitle() == null || eventData.getTitle().trim().isEmpty()) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "事件标题不能为空"));
            }

            if (eventData.getImportance() != null && (eventData.getImportance() < 1 || eventData.getImportance() > 10)) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "重要性必须在1-10之间"));
            }
        }

        // 转换为Event实体列表
        List<Event> events = new ArrayList<>();
        for (BatchImportEventsCommand.EventImportData eventData : command.getEvents()) {
            Event event = new Event();
            event.setYear(eventData.getYear());
            event.setTitleId(eventData.getTitleId());
            event.setTitle(eventData.getTitle());
            event.setDescription(eventData.getDescription());
            event.setLocation(eventData.getLocation());
            event.setCountryId(eventData.getCountryId());
            event.setImportance(eventData.getImportance());
            events.add(event);
        }

        List<Event> createdEvents = eventService.batchCreateEvents(events);
        return ResponseEntity.ok(ApiResponse.success("批量导入成功，共导入 " + createdEvents.size() + " 个事件", createdEvents));
    }
}
