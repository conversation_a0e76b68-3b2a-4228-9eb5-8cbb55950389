package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.*;
import com.celt.historcal.globe.api.entity.Event;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.EventService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/events")
@Tag(name = "事件管理", description = "历史事件管理相关API")
public class EventController {

    private final EventService eventService;

    @Autowired
    public EventController(EventService eventService) {
        this.eventService = eventService;
    }

    @Operation(summary = "获取所有事件", description = "获取系统中所有历史事件的列表，按年份和重要性排序")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class)))
    })
    @PostMapping("/list")
    public ResponseEntity<ApiResponse<List<Event>>> listEvents() {
        List<Event> events = eventService.findAllEvents();
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按ID获取事件", description = "根据事件ID获取特定历史事件信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/get")
    public ResponseEntity<ApiResponse<Event>> getEventById(
            @Parameter(description = "事件ID请求参数") @RequestBody Map<String, Long> request) {

        Long id = request.get("id");
        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        Event event = eventService.findEventById(id);
        if (event == null) {
            throw new ResourceNotFoundException("Event", "id", id);
        }

        return ResponseEntity.ok(ApiResponse.success(event));
    }

    @Operation(summary = "按年份获取事件", description = "根据年份获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByYear(
            @Parameter(description = "年份请求参数") @RequestBody Map<String, Integer> request) {

        Integer year = request.get("year");
        if (year == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        List<Event> events = eventService.findEventsByYear(year);
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按国家ID获取事件", description = "根据国家ID获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-country")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByCountryId(
            @Parameter(description = "国家ID请求参数") @RequestBody Map<String, String> request) {

        String countryId = request.get("countryId");
        if (countryId == null || countryId.trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "国家ID不能为空"));
        }

        List<Event> events = eventService.findEventsByCountryId(countryId);
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按重要性获取事件", description = "根据重要性等级获取历史事件列表（大于等于指定重要性）")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-importance")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByImportance(
            @Parameter(description = "重要性请求参数") @RequestBody Map<String, Integer> request) {

        Integer importance = request.get("importance");
        if (importance == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性不能为空"));
        }

        if (importance < 1 || importance > 10) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
        }

        List<Event> events = eventService.findEventsByImportance(importance);
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "按年份范围获取事件", description = "根据年份范围获取历史事件列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year-range")
    public ResponseEntity<ApiResponse<List<Event>>> getEventsByYearRange(
            @Parameter(description = "年份范围请求参数") @RequestBody Map<String, Integer> request) {

        Integer startYear = request.get("startYear");
        Integer endYear = request.get("endYear");

        if (startYear == null || endYear == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份和结束年份不能为空"));
        }

        if (startYear > endYear) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份不能大于结束年份"));
        }

        List<Event> events = eventService.findEventsByYearRange(startYear, endYear);
        return ResponseEntity.ok(ApiResponse.success(events));
    }

    @Operation(summary = "创建事件", description = "创建新的历史事件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件创建成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<Event>> createEvent(
            @Parameter(description = "事件数据") @RequestBody Event event) {

        if (event == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件数据不能为空"));
        }

        if (event.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (event.getTitle() == null || event.getTitle().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件标题不能为空"));
        }

        if (event.getImportance() != null && (event.getImportance() < 1 || event.getImportance() > 10)) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
        }

        Event createdEvent = eventService.createEvent(event);
        return ResponseEntity.ok(ApiResponse.success("事件创建成功", createdEvent));
    }

    @Operation(summary = "更新事件", description = "更新现有历史事件信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件更新成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = Event.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/update")
    public ResponseEntity<ApiResponse<Event>> updateEvent(
            @Parameter(description = "更新事件请求参数") @RequestBody Map<String, Object> request) {

        Long id = null;
        Object idObj = request.get("id");
        if (idObj instanceof Number) {
            id = ((Number) idObj).longValue();
        }

        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> eventData = (Map<String, Object>) request.get("data");
        if (eventData == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件数据不能为空"));
        }

        Event existingEvent = eventService.findEventById(id);
        if (existingEvent == null) {
            throw new ResourceNotFoundException("Event", "id", id);
        }

        // 构建事件对象
        Event event = new Event();
        event.setId(id);

        Object yearObj = eventData.get("year");
        if (yearObj instanceof Number) {
            event.setYear(((Number) yearObj).intValue());
        }

        event.setTitleId((String) eventData.get("titleId"));
        event.setTitle((String) eventData.get("title"));
        event.setDescription((String) eventData.get("description"));
        event.setLocation((String) eventData.get("location"));
        event.setCountryId((String) eventData.get("countryId"));

        Object importanceObj = eventData.get("importance");
        if (importanceObj instanceof Number) {
            Integer importance = ((Number) importanceObj).intValue();
            if (importance < 1 || importance > 10) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "重要性必须在1-10之间"));
            }
            event.setImportance(importance);
        }

        if (event.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (event.getTitle() == null || event.getTitle().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "事件标题不能为空"));
        }

        Event updatedEvent = eventService.updateEvent(event);
        return ResponseEntity.ok(ApiResponse.success("事件更新成功", updatedEvent));
    }

    @Operation(summary = "删除事件", description = "删除指定历史事件")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "事件删除成功",
                    content = @Content(mediaType = "application/json")),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "事件未找到",
                    content = @Content)
    })
    @PostMapping("/delete")
    public ResponseEntity<ApiResponse<Object>> deleteEvent(
            @Parameter(description = "事件ID请求参数") @RequestBody Map<String, Long> request) {

        Long id = request.get("id");
        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        Event eventToDelete = eventService.findEventById(id);
        if (eventToDelete == null) {
            throw new ResourceNotFoundException("Event", "id", id);
        }

        eventService.deleteEvent(id);
        return ResponseEntity.ok(ApiResponse.success("事件删除成功", null));
    }
}
