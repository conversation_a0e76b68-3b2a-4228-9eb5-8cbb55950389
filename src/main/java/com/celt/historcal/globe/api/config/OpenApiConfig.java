package com.celt.historcal.globe.api.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class OpenApiConfig {

    @Bean
    public OpenAPI historicalGlobeOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Historical Globe API")
                        .description("API for Historical Globe application")
                        .version("v1.0.0")
                        .contact(new Contact()
                                .name("Historical Globe Team")
                                .email("<EMAIL>")
                                .url("https://www.historicalglobe.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")))
                .servers(List.of(
                        new Server().url("http://localhost:8080").description("Development Server"),
                        new Server().url("https://api.historicalglobe.com").description("Production Server")
                ));
    }
}