package com.celt.historcal.globe.api.dto;

import com.celt.historcal.globe.api.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "用户命令请求")
public class UserCommandRequest {
    
    @Schema(description = "操作类型：create-创建用户, update-更新用户, delete-删除用户", example = "create", required = true)
    private String operation;
    
    @Schema(description = "用户ID（当operation为update或delete时必填）", example = "1")
    private Long id;
    
    @Schema(description = "用户数据（当operation为create或update时必填）")
    private User data;
}