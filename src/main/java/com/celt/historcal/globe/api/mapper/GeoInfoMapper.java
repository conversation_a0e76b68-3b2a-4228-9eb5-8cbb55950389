package com.celt.historcal.globe.api.mapper;

import com.celt.historcal.globe.api.entity.GeoInfo;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface GeoInfoMapper {
    
    @Select("SELECT * FROM geo_info ORDER BY year DESC, name ASC")
    List<GeoInfo> findAll();
    
    @Select("SELECT * FROM geo_info WHERE id = #{id}")
    GeoInfo findById(Long id);
    
    @Select("SELECT * FROM geo_info WHERE year = #{year} ORDER BY name ASC")
    List<GeoInfo> findByYear(Integer year);
    
    @Select("SELECT * FROM geo_info WHERE name LIKE CONCAT('%', #{name}, '%') ORDER BY year DESC, name ASC")
    List<GeoInfo> findByNameContaining(String name);
    
    @Select("SELECT * FROM geo_info WHERE abbr = #{abbr} ORDER BY year DESC")
    List<GeoInfo> findByAbbr(String abbr);
    
    @Select("SELECT * FROM geo_info WHERE subject_to = #{subjectTo} ORDER BY year DESC, name ASC")
    List<GeoInfo> findBySubjectTo(String subjectTo);
    
    @Select("SELECT * FROM geo_info WHERE part_of = #{partOf} ORDER BY year DESC, name ASC")
    List<GeoInfo> findByPartOf(String partOf);
    
    @Select("SELECT * FROM geo_info WHERE border_precision >= #{borderPrecision} ORDER BY year DESC, border_precision DESC")
    List<GeoInfo> findByBorderPrecisionGreaterThanEqual(Integer borderPrecision);
    
    @Select("SELECT * FROM geo_info WHERE year BETWEEN #{startYear} AND #{endYear} ORDER BY year DESC, name ASC")
    List<GeoInfo> findByYearRange(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);
    
    @Insert("INSERT INTO geo_info(year, name, abbr, subject_to, border_precision, part_of, coordinates, created_at, updated_at) " +
            "VALUES(#{year}, #{name}, #{abbr}, #{subjectTo}, #{borderPrecision}, #{partOf}, #{coordinates}, #{createdAt}, #{updatedAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(GeoInfo geoInfo);
    
    @Update("UPDATE geo_info SET year = #{year}, name = #{name}, abbr = #{abbr}, " +
            "subject_to = #{subjectTo}, border_precision = #{borderPrecision}, part_of = #{partOf}, " +
            "coordinates = #{coordinates}, updated_at = #{updatedAt} WHERE id = #{id}")
    int update(GeoInfo geoInfo);
    
    @Delete("DELETE FROM geo_info WHERE id = #{id}")
    int deleteById(Long id);
}
