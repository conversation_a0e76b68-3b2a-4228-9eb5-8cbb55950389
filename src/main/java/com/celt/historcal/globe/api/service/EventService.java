package com.celt.historcal.globe.api.service;

import com.celt.historcal.globe.api.entity.Event;
import com.celt.historcal.globe.api.mapper.EventMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class EventService {

    private final EventMapper eventMapper;

    @Autowired
    public EventService(EventMapper eventMapper) {
        this.eventMapper = eventMapper;
    }

    public List<Event> findAllEvents() {
        return eventMapper.findAll();
    }

    public Event findEventById(Long id) {
        return eventMapper.findById(id);
    }

    public List<Event> findEventsByYear(Integer year) {
        return eventMapper.findByYear(year);
    }

    public List<Event> findEventsByCountryId(String countryId) {
        return eventMapper.findByCountryId(countryId);
    }

    public List<Event> findEventsByImportance(Integer importance) {
        return eventMapper.findByImportanceGreaterThanEqual(importance);
    }

    public List<Event> findEventsByYearRange(Integer startYear, Integer endYear) {
        return eventMapper.findByYearRange(startYear, endYear);
    }

    @Transactional
    public Event createEvent(Event event) {
        // 设置创建时间和更新时间为当前时间戳（毫秒）
        long currentTimeMillis = System.currentTimeMillis();
        event.setCreateTime(currentTimeMillis);
        event.setUpdateTime(currentTimeMillis);
        eventMapper.insert(event);
        return event;
    }

    @Transactional
    public Event updateEvent(Event event) {
        // 设置更新时间为当前时间戳（毫秒）
        event.setUpdateTime(System.currentTimeMillis());
        eventMapper.update(event);
        return event;
    }

    @Transactional
    public void deleteEvent(Long id) {
        eventMapper.deleteById(id);
    }

    @Transactional
    public List<Event> batchCreateEvents(List<Event> events) {
        // 设置创建时间和更新时间为当前时间戳（毫秒）
        long currentTimeMillis = System.currentTimeMillis();

        for (Event event : events) {
            event.setCreateTime(currentTimeMillis);
            event.setUpdateTime(currentTimeMillis);
            eventMapper.insert(event);
        }

        return events;
    }
}
