package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.DeleteUserCommand;
import com.celt.historcal.globe.api.entity.User;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户管理相关API")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @Operation(summary = "获取所有用户", description = "获取系统中所有用户的列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class)))
    })
    @PostMapping("/list")
    public ResponseEntity<ApiResponse<List<User>>> listUsers() {
        List<User> users = userService.findAllUsers();
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @Operation(summary = "按ID获取用户", description = "根据用户ID获取特定用户信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/get")
    public ResponseEntity<ApiResponse<User>> getUserById(
            @Parameter(description = "用户ID请求参数") @RequestBody Map<String, Long> request) {

        Long id = request.get("id");
        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        User user = userService.findUserById(id);
        if (user == null) {
            throw new ResourceNotFoundException("User", "id", id);
        }

        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @Operation(summary = "创建用户", description = "创建新用户")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户创建成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<User>> createUser(
            @Parameter(description = "用户数据") @RequestBody User user) {

        if (user == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
        }

        User createdUser = userService.createUser(user);
        return ResponseEntity.ok(ApiResponse.success("用户创建成功", createdUser));
    }

    @Operation(summary = "更新用户", description = "更新现有用户信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户更新成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/update")
    public ResponseEntity<ApiResponse<User>> updateUser(
            @Parameter(description = "更新用户请求参数") @RequestBody Map<String, Object> request) {

        Long id = null;
        Object idObj = request.get("id");
        if (idObj instanceof Number) {
            id = ((Number) idObj).longValue();
        }

        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> userData = (Map<String, Object>) request.get("data");
        if (userData == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
        }

        User existingUser = userService.findUserById(id);
        if (existingUser == null) {
            throw new ResourceNotFoundException("User", "id", id);
        }

        // 构建用户对象
        User user = new User();
        user.setId(id);
        user.setUsername((String) userData.get("username"));
        user.setEmail((String) userData.get("email"));
        user.setPassword((String) userData.get("password"));

        User updatedUser = userService.updateUser(user);
        return ResponseEntity.ok(ApiResponse.success("用户更新成功", updatedUser));
    }

    @Operation(summary = "删除用户", description = "删除指定用户")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户删除成功",
                    content = @Content(mediaType = "application/json")),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/delete")
    public ResponseEntity<ApiResponse<Object>> deleteUser(
            @Parameter(description = "用户ID请求参数") @RequestBody Map<String, Long> request) {

        Long id = request.get("id");
        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        User userToDelete = userService.findUserById(id);
        if (userToDelete == null) {
            throw new ResourceNotFoundException("User", "id", id);
        }

        userService.deleteUser(id);
        return ResponseEntity.ok(ApiResponse.success("用户删除成功", null));
    }
}