package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.UserCommandRequest;
import com.celt.historcal.globe.api.dto.UserQueryRequest;
import com.celt.historcal.globe.api.entity.User;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户管理相关API")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @Operation(summary = "用户查询", description = "执行用户查询操作（获取所有用户或按ID获取用户）")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json", 
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数", 
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到", 
                    content = @Content)
    })
    @PostMapping("/query")
    public ResponseEntity<ApiResponse<?>> query(
            @Parameter(description = "查询请求参数") @RequestBody UserQueryRequest request) {
        
        switch (request.getOperation()) {
            case "findAll":
                List<User> users = userService.findAllUsers();
                return ResponseEntity.ok(ApiResponse.success(users));
                
            case "findById":
                if (request.getId() == null) {
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
                }
                User user = userService.findUserById(request.getId());
                if (user == null) {
                    throw new ResourceNotFoundException("User", "id", request.getId());
                }
                return ResponseEntity.ok(ApiResponse.success(user));
                
            default:
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "不支持的操作类型: " + request.getOperation()));
        }
    }

    @Operation(summary = "用户命令", description = "执行用户命令操作（创建、更新、删除用户）")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "命令执行成功",
                    content = @Content(mediaType = "application/json", 
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数", 
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到", 
                    content = @Content)
    })
    @PostMapping("/command")
    public ResponseEntity<ApiResponse<?>> command(
            @Parameter(description = "命令请求参数") @RequestBody UserCommandRequest request) {
        
        switch (request.getOperation()) {
            case "create":
                if (request.getData() == null) {
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
                }
                User createdUser = userService.createUser(request.getData());
                return new ResponseEntity<>(ApiResponse.success("用户创建成功", createdUser), HttpStatus.OK);
                
            case "update":
                if (request.getId() == null) {
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
                }
                if (request.getData() == null) {
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
                }
                User existingUser = userService.findUserById(request.getId());
                if (existingUser == null) {
                    throw new ResourceNotFoundException("User", "id", request.getId());
                }
                request.getData().setId(request.getId());
                User updatedUser = userService.updateUser(request.getData());
                return ResponseEntity.ok(ApiResponse.success("用户更新成功", updatedUser));
                
            case "delete":
                if (request.getId() == null) {
                    return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
                }
                User userToDelete = userService.findUserById(request.getId());
                if (userToDelete == null) {
                    throw new ResourceNotFoundException("User", "id", request.getId());
                }
                userService.deleteUser(request.getId());
                return ResponseEntity.ok(ApiResponse.success("用户删除成功", null));
                
            default:
                return ResponseEntity.badRequest().body(ApiResponse.error(400, "不支持的操作类型: " + request.getOperation()));
        }
    }
}