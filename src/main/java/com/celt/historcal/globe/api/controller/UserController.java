package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.CreateUserCommand;
import com.celt.historcal.globe.api.dto.DeleteUserCommand;
import com.celt.historcal.globe.api.dto.GetUserByIdQuery;
import com.celt.historcal.globe.api.dto.UpdateUserCommand;
import com.celt.historcal.globe.api.entity.User;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户管理相关API")
public class UserController {

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @Operation(summary = "获取所有用户", description = "获取系统中所有用户的列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class)))
    })
    @PostMapping("/list")
    public ResponseEntity<ApiResponse<List<User>>> listUsers() {
        List<User> users = userService.findAllUsers();
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @Operation(summary = "按ID获取用户", description = "根据用户ID获取特定用户信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/get")
    public ResponseEntity<ApiResponse<User>> getUserById(
            @Parameter(description = "按ID获取用户查询") @RequestBody GetUserByIdQuery query) {

        if (query.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        User user = userService.findUserById(query.getId());
        if (user == null) {
            throw new ResourceNotFoundException("User", "id", query.getId());
        }

        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @Operation(summary = "创建用户", description = "创建新用户")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户创建成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<User>> createUser(
            @Parameter(description = "用户数据") @RequestBody User user) {

        if (user == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
        }

        User createdUser = userService.createUser(user);
        return ResponseEntity.ok(ApiResponse.success("用户创建成功", createdUser));
    }

    @Operation(summary = "更新用户", description = "更新现有用户信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户更新成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = User.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/update")
    public ResponseEntity<ApiResponse<User>> updateUser(
            @Parameter(description = "更新用户命令") @RequestBody UpdateUserCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        if (command.getData() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "用户数据不能为空"));
        }

        User existingUser = userService.findUserById(command.getId());
        if (existingUser == null) {
            throw new ResourceNotFoundException("User", "id", command.getId());
        }

        // 设置ID到用户数据中
        command.getData().setId(command.getId());
        User updatedUser = userService.updateUser(command.getData());
        return ResponseEntity.ok(ApiResponse.success("用户更新成功", updatedUser));
    }

    @Operation(summary = "删除用户", description = "删除指定用户")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "用户删除成功",
                    content = @Content(mediaType = "application/json")),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "用户未找到",
                    content = @Content)
    })
    @PostMapping("/delete")
    public ResponseEntity<ApiResponse<Object>> deleteUser(
            @Parameter(description = "删除用户命令") @RequestBody DeleteUserCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        User userToDelete = userService.findUserById(command.getId());
        if (userToDelete == null) {
            throw new ResourceNotFoundException("User", "id", command.getId());
        }

        userService.deleteUser(command.getId());
        return ResponseEntity.ok(ApiResponse.success("用户删除成功", null));
    }
}