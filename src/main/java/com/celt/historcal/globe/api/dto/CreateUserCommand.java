package com.celt.historcal.globe.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建用户命令")
public class CreateUserCommand {
    
    @Schema(description = "用户名", example = "zhang<PERSON>", required = true)
    private String username;
    
    @Schema(description = "电子邮箱", example = "<EMAIL>", required = true)
    private String email;
    
    @Schema(description = "密码", example = "password123", required = true)
    private String password;
}
