package com.celt.historcal.globe.api.dto;

import com.celt.historcal.globe.api.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "更新用户命令")
public class UpdateUserCommand {
    
    @Schema(description = "用户ID", example = "1", required = true)
    private Long id;
    
    @Schema(description = "用户数据", required = true)
    private User data;
}
