package com.celt.historcal.globe.api.controller;

import com.celt.historcal.globe.api.common.ApiResponse;
import com.celt.historcal.globe.api.dto.*;
import com.celt.historcal.globe.api.entity.GeoInfo;
import com.celt.historcal.globe.api.exception.ResourceNotFoundException;
import com.celt.historcal.globe.api.service.GeoInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/geo-infos")
@Tag(name = "地理信息管理", description = "地理信息管理相关API")
public class GeoInfoController {

    private final GeoInfoService geoInfoService;

    @Autowired
    public GeoInfoController(GeoInfoService geoInfoService) {
        this.geoInfoService = geoInfoService;
    }

    @Operation(summary = "获取所有地理信息", description = "获取系统中所有地理信息的列表，按年份和名称排序")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class)))
    })
    @PostMapping("/list")
    public ResponseEntity<ApiResponse<List<GeoInfo>>> listGeoInfos() {
        List<GeoInfo> geoInfos = geoInfoService.findAllGeoInfos();
        return ResponseEntity.ok(ApiResponse.success(geoInfos));
    }

    @Operation(summary = "按ID获取地理信息", description = "根据地理信息ID获取特定地理信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "地理信息未找到",
                    content = @Content)
    })
    @PostMapping("/get")
    public ResponseEntity<ApiResponse<GeoInfo>> getGeoInfoById(
            @Parameter(description = "按ID获取地理信息查询") @RequestBody GetGeoInfoByIdQuery query) {

        if (query.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        GeoInfo geoInfo = geoInfoService.findGeoInfoById(query.getId());
        if (geoInfo == null) {
            throw new ResourceNotFoundException("GeoInfo", "id", query.getId());
        }

        return ResponseEntity.ok(ApiResponse.success(geoInfo));
    }

    @Operation(summary = "按年份获取地理信息", description = "根据年份获取地理信息列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year")
    public ResponseEntity<ApiResponse<List<GeoInfo>>> getGeoInfosByYear(
            @Parameter(description = "按年份获取地理信息查询") @RequestBody GetGeoInfosByYearQuery query) {

        if (query.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        List<GeoInfo> geoInfos = geoInfoService.findGeoInfosByYear(query.getYear());
        return ResponseEntity.ok(ApiResponse.success(geoInfos));
    }

    @Operation(summary = "按名称搜索地理信息", description = "根据名称关键字搜索地理信息列表（支持模糊搜索）")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/search-by-name")
    public ResponseEntity<ApiResponse<List<GeoInfo>>> getGeoInfosByName(
            @Parameter(description = "按名称搜索地理信息查询") @RequestBody GetGeoInfosByNameQuery query) {

        if (query.getName() == null || query.getName().trim().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "名称关键字不能为空"));
        }

        List<GeoInfo> geoInfos = geoInfoService.findGeoInfosByNameContaining(query.getName());
        return ResponseEntity.ok(ApiResponse.success(geoInfos));
    }

    @Operation(summary = "按年份范围获取地理信息", description = "根据年份范围获取地理信息列表")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/get-by-year-range")
    public ResponseEntity<ApiResponse<List<GeoInfo>>> getGeoInfosByYearRange(
            @Parameter(description = "按年份范围获取地理信息查询") @RequestBody GetGeoInfosByYearRangeQuery query) {

        if (query.getStartYear() == null || query.getEndYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份和结束年份不能为空"));
        }

        if (query.getStartYear() > query.getEndYear()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "开始年份不能大于结束年份"));
        }

        List<GeoInfo> geoInfos = geoInfoService.findGeoInfosByYearRange(query.getStartYear(), query.getEndYear());
        return ResponseEntity.ok(ApiResponse.success(geoInfos));
    }

    @Operation(summary = "创建地理信息", description = "创建新的地理信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "地理信息创建成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/create")
    public ResponseEntity<ApiResponse<GeoInfo>> createGeoInfo(
            @Parameter(description = "创建地理信息命令") @RequestBody CreateGeoInfoCommand command) {

        if (command == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "地理信息数据不能为空"));
        }

        if (command.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (command.getBorderPrecision() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "边界精度不能为空"));
        }

        if (command.getBorderPrecision() < 1 || command.getBorderPrecision() > 10) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "边界精度必须在1-10之间"));
        }

        // 将command转换为GeoInfo实体
        GeoInfo geoInfo = new GeoInfo();
        geoInfo.setYear(command.getYear());
        geoInfo.setName(command.getName());
        geoInfo.setAbbr(command.getAbbr());
        geoInfo.setSubjectTo(command.getSubjectTo());
        geoInfo.setBorderPrecision(command.getBorderPrecision());
        geoInfo.setPartOf(command.getPartOf());
        geoInfo.setCoordinates(command.getCoordinates());

        GeoInfo createdGeoInfo = geoInfoService.createGeoInfo(geoInfo);
        return ResponseEntity.ok(ApiResponse.success("地理信息创建成功", createdGeoInfo));
    }
    @Operation(summary = "更新地理信息", description = "更新现有地理信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "地理信息更新成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "地理信息未找到",
                    content = @Content)
    })
    @PostMapping("/update")
    public ResponseEntity<ApiResponse<GeoInfo>> updateGeoInfo(
            @Parameter(description = "更新地理信息命令") @RequestBody UpdateGeoInfoCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        if (command.getData() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "地理信息数据不能为空"));
        }

        GeoInfo existingGeoInfo = geoInfoService.findGeoInfoById(command.getId());
        if (existingGeoInfo == null) {
            throw new ResourceNotFoundException("GeoInfo", "id", command.getId());
        }

        UpdateGeoInfoCommand.GeoInfoData geoInfoData = command.getData();

        if (geoInfoData.getYear() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "年份不能为空"));
        }

        if (geoInfoData.getBorderPrecision() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "边界精度不能为空"));
        }

        if (geoInfoData.getBorderPrecision() < 1 || geoInfoData.getBorderPrecision() > 10) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "边界精度必须在1-10之间"));
        }

        // 构建地理信息对象
        GeoInfo geoInfo = new GeoInfo();
        geoInfo.setId(command.getId());
        geoInfo.setYear(geoInfoData.getYear());
        geoInfo.setName(geoInfoData.getName());
        geoInfo.setAbbr(geoInfoData.getAbbr());
        geoInfo.setSubjectTo(geoInfoData.getSubjectTo());
        geoInfo.setBorderPrecision(geoInfoData.getBorderPrecision());
        geoInfo.setPartOf(geoInfoData.getPartOf());
        geoInfo.setCoordinates(geoInfoData.getCoordinates());

        GeoInfo updatedGeoInfo = geoInfoService.updateGeoInfo(geoInfo);
        return ResponseEntity.ok(ApiResponse.success("地理信息更新成功", updatedGeoInfo));
    }

    @Operation(summary = "删除地理信息", description = "删除指定地理信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "地理信息删除成功",
                    content = @Content(mediaType = "application/json")),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "地理信息未找到",
                    content = @Content)
    })
    @PostMapping("/delete")
    public ResponseEntity<ApiResponse<Object>> deleteGeoInfo(
            @Parameter(description = "删除地理信息命令") @RequestBody DeleteGeoInfoCommand command) {

        if (command.getId() == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "ID不能为空"));
        }

        GeoInfo geoInfoToDelete = geoInfoService.findGeoInfoById(command.getId());
        if (geoInfoToDelete == null) {
            throw new ResourceNotFoundException("GeoInfo", "id", command.getId());
        }

        geoInfoService.deleteGeoInfo(command.getId());
        return ResponseEntity.ok(ApiResponse.success("地理信息删除成功", null));
    }
    @Operation(summary = "批量导入地理信息", description = "批量导入多个地理信息")
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "批量导入成功",
                    content = @Content(mediaType = "application/json",
                    schema = @Schema(implementation = GeoInfo.class))),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "无效的请求参数",
                    content = @Content)
    })
    @PostMapping("/batch-import")
    public ResponseEntity<ApiResponse<List<GeoInfo>>> batchImportGeoInfos(
            @Parameter(description = "批量导入地理信息命令") @RequestBody BatchImportGeoInfosCommand command) {

        if (command == null || command.getGeoInfos() == null || command.getGeoInfos().isEmpty()) {
            return ResponseEntity.badRequest().body(ApiResponse.error(400, "地理信息列表不能为空"));
        }

        // 验证每个地理信息数据
        for (int i = 0; i < command.getGeoInfos().size(); i++) {
            BatchImportGeoInfosCommand.GeoInfoImportData geoInfoData = command.getGeoInfos().get(i);
            String prefix = "第" + (i + 1) + "个地理信息：";

            if (geoInfoData.getYear() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "年份不能为空"));
            }

            if (geoInfoData.getBorderPrecision() == null) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "边界精度不能为空"));
            }

            if (geoInfoData.getBorderPrecision() < 1 || geoInfoData.getBorderPrecision() > 10) {
                return ResponseEntity.badRequest().body(ApiResponse.error(400, prefix + "边界精度必须在1-10之间"));
            }
        }

        // 转换为GeoInfo实体列表
        List<GeoInfo> geoInfos = new ArrayList<>();
        for (BatchImportGeoInfosCommand.GeoInfoImportData geoInfoData : command.getGeoInfos()) {
            GeoInfo geoInfo = new GeoInfo();
            geoInfo.setYear(geoInfoData.getYear());
            geoInfo.setName(geoInfoData.getName());
            geoInfo.setAbbr(geoInfoData.getAbbr());
            geoInfo.setSubjectTo(geoInfoData.getSubjectTo());
            geoInfo.setBorderPrecision(geoInfoData.getBorderPrecision());
            geoInfo.setPartOf(geoInfoData.getPartOf());
            geoInfo.setCoordinates(geoInfoData.getCoordinates());
            geoInfos.add(geoInfo);
        }

        List<GeoInfo> createdGeoInfos = geoInfoService.batchCreateGeoInfos(geoInfos);
        return ResponseEntity.ok(ApiResponse.success("批量导入成功，共导入 " + createdGeoInfos.size() + " 个地理信息", createdGeoInfos));
    }
}