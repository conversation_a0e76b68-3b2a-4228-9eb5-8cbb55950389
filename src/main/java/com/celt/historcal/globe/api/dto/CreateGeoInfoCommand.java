package com.celt.historcal.globe.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "创建地理信息命令")
public class CreateGeoInfoCommand {
    
    @Schema(description = "年份", example = "2023", required = true)
    private Integer year;
    
    @Schema(description = "名称", example = "中华人民共和国")
    private String name;
    
    @Schema(description = "简称", example = "中国")
    private String abbr;
    
    @Schema(description = "隶属于", example = "亚洲")
    private String subjectTo;
    
    @Schema(description = "边界精度", example = "5", required = true)
    private Integer borderPrecision;
    
    @Schema(description = "属于", example = "联合国")
    private String partOf;
    
    @Schema(description = "坐标信息", example = "经纬度坐标数据")
    private String coordinates;
}
