package com.celt.historcal.globe.api.service;

import com.celt.historcal.globe.api.entity.User;
import com.celt.historcal.globe.api.mapper.UserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class UserService {

    private final UserMapper userMapper;

    @Autowired
    public UserService(UserMapper userMapper) {
        this.userMapper = userMapper;
    }

    public List<User> findAllUsers() {
        return userMapper.findAll();
    }

    public User findUserById(Long id) {
        return userMapper.findById(id);
    }

    @Transactional
    public User createUser(User user) {
        // 设置创建时间和更新时间为当前时间戳（毫秒）
        long currentTimeMillis = System.currentTimeMillis();
        user.setCreatedAt(currentTimeMillis);
        user.setUpdatedAt(currentTimeMillis);
        userMapper.insert(user);
        return user;
    }

    @Transactional
    public User updateUser(User user) {
        // 设置更新时间为当前时间戳（毫秒）
        user.setUpdatedAt(System.currentTimeMillis());
        userMapper.update(user);
        return user;
    }

    @Transactional
    public void deleteUser(Long id) {
        userMapper.deleteById(id);
    }
}