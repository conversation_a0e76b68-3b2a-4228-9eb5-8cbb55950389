CREATE DATABASE IF NOT EXISTS historical_globe;

USE historical_globe;

CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入一些示例数据
INSERT INTO users (username, email, password, created_at, updated_at)
VALUES
    ('user1', '<EMAIL>', 'password1', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    ('user2', '<EMAIL>', 'password2', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    ('user3', '<EMAIL>', 'password3', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000);