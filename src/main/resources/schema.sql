CREATE DATABASE IF NOT EXISTS historical_globe;

USE historical_globe;

CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建事件表
CREATE TABLE IF NOT EXISTS events (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    year INT NOT NULL COMMENT '年份',
    title_id VARCHAR(255) COMMENT '事件标题ID',
    title VARCHAR(255) COMMENT '事件标题',
    description TEXT COMMENT '事件描述',
    location VARCHAR(255) COMMENT '事件发生地点',
    country_id VARCHAR(255) COMMENT '国家ID',
    importance INT COMMENT '事件重要性',
    create_time BIGINT COMMENT '创建时间',
    update_time BIGINT COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 创建国家信息表
CREATE TABLE geo_info (
                          id bigint auto_increment PRIMARY KEY,
                          year INT NOT NULL comment "年份",
                          name varchar(255) comment "名称",
                          abbr VARCHAR(255) comment "简称",
                          subject_to VARCHAR(255),
                          border_precision INT NOT NULL comment "边界精度",
                          part_of VARCHAR(255) comment "属于",
                          coordinates TEXT comment "坐标",
                          create_time BIGINT not null comment "创建时间",
                          update_time BIGINT not null comment "更新时间"
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 插入一些示例数据
INSERT INTO users (username, email, password, created_at, updated_at)
VALUES
    ('user1', '<EMAIL>', 'password1', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    ('user2', '<EMAIL>', 'password2', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    ('user3', '<EMAIL>', 'password3', UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000);

-- 插入一些事件示例数据
INSERT INTO events (year, title_id, title, description, location, country_id, importance, create_time, update_time)
VALUES
    (2023, 'EVENT_001', '重要科技突破', '人工智能技术取得重大突破', '北京', 'CN', 9, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    (2022, 'EVENT_002', '历史性会议', '重要的国际会议在此举行', '上海', 'CN', 8, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    (2021, 'EVENT_003', '文化遗产发现', '发现了重要的历史文化遗产', '西安', 'CN', 7, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    (2020, 'EVENT_004', '全球性事件', '影响全球的重要事件', '纽约', 'US', 10, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000),
    (2019, 'EVENT_005', '环境保护行动', '大规模环境保护行动启动', '伦敦', 'UK', 6, UNIX_TIMESTAMP()*1000, UNIX_TIMESTAMP()*1000);