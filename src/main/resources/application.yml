server:
  port: 8080

spring:
  datasource:
    # 数据库连接URL
    url: *********************************************************************************************************************************
    # 数据库用户名
    username: root
    # 数据库密码
    password:
    # 数据库驱动类名
    driver-class-name: com.mysql.cj.jdbc.Driver
  
mybatis:
  # MyBatis映射文件位置
  mapper-locations: classpath:mapper/*.xml
  # 实体类包路径
  type-aliases-package: com.celt.historcal.globe.api.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 日志实现类
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# Springdoc OpenAPI配置
springdoc:
  api-docs:
    # API文档路径
    path: /api-docs
  swagger-ui:
    # Swagger UI路径
    path: /swagger-ui.html
    # 操作排序方式
    operationsSorter: method
    # 标签排序方式
    tagsSorter: alpha
    # 启用Try It Out功能
    tryItOutEnabled: true
  # 要扫描的包
  packages-to-scan: com.celt.historcal.globe.api.controller